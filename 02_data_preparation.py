import pandas as pd
import numpy as np
import re
import os

# --- 0. <PERSON><PERSON><PERSON> thư mục output ---
OUTPUT_DIR = "output"
if not os.path.exists(OUTPUT_DIR):
    os.makedirs(OUTPUT_DIR)

# --- 1. <PERSON><PERSON><PERSON> dữ liệu ---
DATA_DIR = "data/ml-latest-small"
movies_df = pd.read_csv(os.path.join(DATA_DIR, "movies.csv"))
ratings_df = pd.read_csv(os.path.join(DATA_DIR, "ratings.csv"))

# --- 2. Hợp nhất DataFrames ---
df = pd.merge(ratings_df, movies_df, on='movieId', how='left')

# --- 3. <PERSON><PERSON> thuật Đặc trưng (Feature Engineering) ---
def extract_year(title):
    match = re.search(r'\((\d{4})\)', title)
    if match:
        return int(match.group(1))
    return np.nan

df['year'] = df['title'].apply(extract_year)
df['title_cleaned'] = df['title'].str.replace(r'\s*\(\d{4}\)$', '', regex=True).str.strip()
df['year'].fillna(df['year'].median(), inplace=True)

df['genres_list'] = df['genres'].apply(lambda x: x.split('|'))

df['rating_datetime'] = pd.to_datetime(df['timestamp'], unit='s')
df['rating_year'] = df['rating_datetime'].dt.year
df['rating_month'] = df['rating_datetime'].dt.month
df['rating_day_of_week'] = df['rating_datetime'].dt.dayofweek

# --- 4. Chuẩn bị DataFrame cuối cùng ---
df_prepared = df.drop(['timestamp', 'rating_datetime', 'title', 'genres'], axis=1)
df_prepared.to_csv(os.path.join(OUTPUT_DIR, "movielens_prepared.csv"), index=False)
print("Đã lưu DataFrame đã chuẩn bị vào output/movielens_prepared.csv")

# --- 5. Chuẩn bị dữ liệu cho các mô hình cụ thể ---
# Cho mô hình Lọc Nội dung
movies_for_content = df_prepared[['movieId', 'title_cleaned', 'genres_list']].drop_duplicates(subset='movieId').reset_index(drop=True)
def create_soup(x):
    genres_str = ' '.join(x['genres_list']).replace(' ', '')
    return x['title_cleaned'] + ' ' + genres_str
movies_for_content['soup'] = movies_for_content.apply(create_soup, axis=1)
movies_for_content.to_csv(os.path.join(OUTPUT_DIR, "movies_for_content_based.csv"), index=False)
print("Đã tạo và lưu dữ liệu cho mô hình Lọc Nội dung.")

print("Chuẩn bị dữ liệu hoàn tất.")