import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import requests
import zipfile
import io
import os

# --- 0. Tạo thư mục output ---
OUTPUT_DIR = "output"
if not os.path.exists(OUTPUT_DIR):
    os.makedirs(OUTPUT_DIR)

# --- 1. Tải và giải nén dữ liệu ---
DATA_DIR = "data"
if not os.path.exists(DATA_DIR):
    os.makedirs(DATA_DIR)

DATASET_URL = "http://files.grouplens.org/datasets/movielens/ml-latest-small.zip"
DATASET_PATH = os.path.join(DATA_DIR, "ml-latest-small.zip")
EXTRACTED_PATH = os.path.join(DATA_DIR, "ml-latest-small")

if not os.path.exists(EXTRACTED_PATH):
    print("Đang tải xuống bộ dữ liệu MovieLens...")
    try:
        r = requests.get(DATASET_URL)
        r.raise_for_status()
        z = zipfile.ZipFile(io.BytesIO(r.content))
        z.extractall(DATA_DIR)
        print("Tải xuống và giải nén thành công!")
    except requests.exceptions.RequestException as e:
        print(f"Lỗi khi tải dữ liệu: {e}")
        exit()
else:
    print("Bộ dữ liệu đã tồn tại.")

# --- 2. Đọc dữ liệu vào Pandas DataFrames ---
try:
    movies_df = pd.read_csv(os.path.join(EXTRACTED_PATH, "movies.csv"))
    ratings_df = pd.read_csv(os.path.join(EXTRACTED_PATH, "ratings.csv"))
    print("\nĐã tải thành công tệp movies.csv và ratings.csv.")
except FileNotFoundError as e:
    print(f"Lỗi: Không tìm thấy tệp CSV. {e}")
    exit()

# --- 3. Hiển thị thông tin ban đầu ---
print("\n5 dòng đầu tiên của movies_df:")
print(movies_df.head())
print("\n5 dòng đầu tiên của ratings_df:")
print(ratings_df.head())

# --- 4. Phân tích Khám phá Dữ liệu (EDA) ---
sns.set_style("whitegrid")
plt.rcParams['figure.figsize'] = (12, 8)

print("\nThông tin tổng quan về movies_df:")
movies_df.info()
print("\nThông tin tổng quan về ratings_df:")
ratings_df.info()
print("\nThống kê mô tả cho ratings_df:")
print(ratings_df.describe())

# Phân phối Đánh giá
plt.figure()
sns.countplot(x='rating', data=ratings_df, palette='viridis')
plt.title('Phân phối của các điểm đánh giá', fontsize=16)
plt.xlabel('Điểm đánh giá', fontsize=12)
plt.ylabel('Số lượng', fontsize=12)
plt.savefig(os.path.join(OUTPUT_DIR, 'ratings_distribution.png'))
plt.show()

# Phân tích Mức độ Phổ biến của Phim
movie_rating_counts = ratings_df['movieId'].value_counts()
plt.figure()
sns.histplot(movie_rating_counts, bins=50, kde=False)
plt.title('Phân phối số lượng đánh giá trên mỗi phim (Đuôi dài)', fontsize=16)
plt.xlabel('Số lượng đánh giá', fontsize=12)
plt.ylabel('Số lượng phim', fontsize=12)
plt.yscale('log')
plt.savefig(os.path.join(OUTPUT_DIR, 'movie_popularity_distribution.png'))
plt.show()

# Phân tích Hoạt động của Người dùng
user_rating_counts = ratings_df['userId'].value_counts()
plt.figure()
sns.histplot(user_rating_counts, bins=50, kde=False)
plt.title('Phân phối số lượng đánh giá trên mỗi người dùng', fontsize=16)
plt.xlabel('Số lượng đánh giá', fontsize=12)
plt.ylabel('Số lượng người dùng', fontsize=12)
plt.yscale('log')
plt.savefig(os.path.join(OUTPUT_DIR, 'user_activity_distribution.png'))
plt.show()

# Phân tích Thể loại
genres_df = movies_df['genres'].str.get_dummies(sep='|')
genre_counts = genres_df.sum().sort_values(ascending=False)
plt.figure(figsize=(15, 10))
sns.barplot(x=genre_counts.values, y=genre_counts.index, palette='mako')
plt.title('Số lượng phim theo từng thể loại', fontsize=16)
plt.xlabel('Số lượng phim', fontsize=12)
plt.ylabel('Thể loại', fontsize=12)
plt.savefig(os.path.join(OUTPUT_DIR, 'genre_distribution.png'))
plt.show()

print("Phân tích khám phá dữ liệu hoàn tất. Các biểu đồ đã được lưu vào thư mục 'output'.")