import pandas as pd
import numpy as np
import pickle
from collections import defaultdict
import os
import matplotlib.pyplot as plt
import seaborn as sns

# --- 1. <PERSON><PERSON><PERSON> c<PERSON>c mô hình và dữ liệu ---
MODEL_DIR = "models"
OUTPUT_DIR = "output"

with open(os.path.join(MODEL_DIR, 'content_cosine_sim.pkl'), 'rb') as f:
    content_sim_matrix = pickle.load(f)
with open(os.path.join(MODEL_DIR, 'content_movies_df.pkl'), 'rb') as f:
    content_movies_df = pickle.load(f)
content_indices = pd.Series(content_movies_df.index, index=content_movies_df['title_cleaned']).drop_duplicates()

with open(os.path.join(MODEL_DIR, 'svd_model.pkl'), 'rb') as f:
    svd_model = pickle.load(f)
with open(os.path.join(MODEL_DIR, 'svd_movies_df.pkl'), 'rb') as f:
    svd_movies_df = pickle.load(f)

# --- 2. <PERSON><PERSON> tích <PERSON>hiên vị <PERSON> biến ---
print("<PERSON><PERSON> phân tích thiên vị phổ biến...")
all_users = svd_movies_df['userId'].unique()
all_svd_recs =
for user_id in all_users:
    seen_movies = svd_movies_df[svd_movies_df['userId'] == user_id]['movieId'].unique()
    all_movies = svd_movies_df['movieId'].unique()
    unseen_movies = np.setdiff1d(all_movies, seen_movies)
    preds = [svd_model.predict(user_id, movie_id) for movie_id in unseen_movies]
    preds.sort(key=lambda x: x.est, reverse=True)
    all_svd_recs.extend([pred.iid for pred in preds[:10]])

recommendation_counts = pd.Series(all_svd_recs).value_counts()
original_popularity = svd_movies_df['movieId'].value_counts()

rec_pop_df = pd.DataFrame({
    'recommendation_freq': recommendation_counts,
    'original_popularity': original_popularity
}).fillna(0)

plt.figure(figsize=(12, 6))
sns.scatterplot(data=rec_pop_df, x='original_popularity', y='recommendation_freq', alpha=0.5)
plt.title('Tương quan giữa Độ phổ biến gốc và Tần suất đề xuất', fontsize=16)
plt.xlabel('Số lượng đánh giá gốc (Log Scale)', fontsize=12)
plt.ylabel('Số lần được đề xuất (Log Scale)', fontsize=12)
plt.xscale('log')
plt.yscale('log')
plt.savefig(os.path.join(OUTPUT_DIR, 'popularity_bias_scatter.png'))
plt.show()
print("Phân tích thiên vị hoàn tất.")