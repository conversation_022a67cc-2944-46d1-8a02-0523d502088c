import pandas as pd
from surprise import Dataset, Reader, SVD
from surprise.model_selection import train_test_split
from surprise import accuracy
import pickle
import os

# --- 0. <PERSON><PERSON><PERSON> thư mục models ---
MODEL_DIR = "models"
if not os.path.exists(MODEL_DIR):
    os.makedirs(MODEL_DIR)

# --- 1. <PERSON><PERSON><PERSON> dữ liệu ---
DATA_FILE = "output/movielens_prepared.csv"
df_prepared = pd.read_csv(DATA_FILE)

# --- 2. <PERSON><PERSON><PERSON> bị dữ liệu cho Surprise ---
ratings_for_surprise = df_prepared[['userId', 'movieId', 'rating']]
reader = Reader(rating_scale=(0.5, 5.0))
data = Dataset.load_from_df(ratings_for_surprise, reader)

# --- 3. <PERSON><PERSON> chia tập huấn luyện và kiểm tra ---
trainset, testset = train_test_split(data, test_size=0.2, random_state=42)

# --- 4. <PERSON><PERSON><PERSON> luyện mô hình SVD ---
print("Bắt đầu huấn luyện mô hình SVD...")
algo = SVD(n_factors=100, n_epochs=20, random_state=42)
algo.fit(trainset)
print("Huấn luyện hoàn tất.")

# --- 5. Đánh giá mô hình ---
predictions = algo.test(testset)
print("\nĐánh giá trên tập kiểm tra:")
rmse = accuracy.rmse(predictions)
mae = accuracy.mae(predictions)
print(f"RMSE: {rmse}")
print(f"MAE: {mae}")

# --- 6. Lưu mô hình ---
pickle.dump(algo, open(os.path.join(MODEL_DIR, 'svd_model.pkl'), 'wb'))
pickle.dump(df_prepared, open(os.path.join(MODEL_DIR, 'svd_movies_df.pkl'), 'wb'))
print("\nĐã lưu mô hình SVD và DataFrame.")