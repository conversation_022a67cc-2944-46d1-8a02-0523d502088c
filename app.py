import streamlit as st
import pandas as pd
import pickle
import requests
import os

# --- Caching: <PERSON><PERSON><PERSON> mô hình và dữ liệu chỉ một lần ---
@st.cache_resource
def load_model_and_data():
    model_path = os.path.join('models', 'svd_model.pkl')
    df_path = os.path.join('models', 'svd_movies_df.pkl')
    
    with open(model_path, 'rb') as f:
        model = pickle.load(f)
    with open(df_path, 'rb') as f:
        df = pickle.load(f)
        
    return model, df

# --- H<PERSON>m gọi API để lấy poster phim ---
def fetch_poster(movie_id, links_df):
    try:
        tmdb_id = links_df[links_df['movieId'] == movie_id]['tmdbId'].iloc
        api_key = st.secrets.get("TMDB_API_KEY", os.environ.get("TMDB_API_KEY"))
        if not api_key:
            return "https://via.placeholder.com/500x750.png?text=No+API+Key"
            
        url = f"https://api.themoviedb.org/3/movie/{tmdb_id}?api_key={api_key}&language=en-US"
        response = requests.get(url)
        response.raise_for_status()
        data = response.json()
        poster_path = data.get('poster_path')
        if poster_path:
            return "https://image.tmdb.org/t/p/w500/" + poster_path
        else:
            return "https://via.placeholder.com/500x750.png?text=Poster+Not+Found"
    except (requests.exceptions.RequestException, IndexError) as e:
        print(f"Lỗi khi lấy poster cho movieId {movie_id}: {e}")
        return "https://via.placeholder.com/500x750.png?text=Error"

# --- Hàm tạo đề xuất ---
def get_recommendations_for_app(user_id, model, df, n=10):
    all_movie_ids = df['movieId'].unique()
    seen_movie_ids = df[df['userId'] == user_id]['movieId'].unique()
    unseen_movie_ids = [movie_id for movie_id in all_movie_ids if movie_id not in seen_movie_ids]
    
    predictions = [model.predict(user_id, movie_id) for movie_id in unseen_movie_ids]
    predictions.sort(key=lambda x: x.est, reverse=True)
    
    top_n_predictions = predictions[:n]
    top_n_movie_ids = [pred.iid for pred in top_n_predictions]
    
    recommended_movies = df[df['movieId'].isin(top_n_movie_ids)][['movieId', 'title_cleaned']].drop_duplicates()
    
    recommended_movies['order'] = recommended_movies['movieId'].apply(lambda x: top_n_movie_ids.index(x))
    recommended_movies = recommended_movies.sort_values('order').reset_index(drop=True)
    
    return recommended_movies

# --- Giao diện ứng dụng Streamlit ---
st.set_page_config(layout="wide")
st.title('🎬 Hệ thống Giới thiệu Phim')
st.markdown("""
Chào mừng bạn đến với hệ thống giới thiệu phim được xây dựng dựa trên thuật toán Lọc Cộng tác (SVD).
Chọn một người dùng từ danh sách bên dưới để xem các đề xuất phim được cá nhân hóa cho họ.
""")

# Tải mô hình và dữ liệu
svd_model, movies_df = load_model_and_data()

# Tải file links.csv để lấy tmdbId
links_df = pd.read_csv(os.path.join('data/ml-latest-small', 'links.csv'))

# Chọn người dùng
user_ids = sorted(movies_df['userId'].unique())
selected_user_id = st.selectbox(
    'Chọn một User ID để nhận đề xuất:',
    user_ids
)

if st.button('Hiển thị Đề xuất', type="primary"):
    with st.spinner('Đang tạo đề xuất cho bạn...'):
        recommendations = get_recommendations_for_app(selected_user_id, svd_model, movies_df, n=10)

        st.subheader(f'Top 10 phim đề xuất cho User ID: {selected_user_id}')

        if not recommendations.empty:
            num_cols = 5
            
            rows = [recommendations.iloc[i:i+num_cols] for i in range(0, len(recommendations), num_cols)]
            for r in rows:
                cols = st.columns(num_cols)
                for i, (_, row) in enumerate(r.iterrows()):
                    with cols[i]:
                        st.markdown(f"**{row['title_cleaned']}**")
                        st.image(fetch_poster(row['movieId'], links_df), use_column_width=True)
        else:
            st.warning("Không thể tạo đề xuất cho người dùng này.")