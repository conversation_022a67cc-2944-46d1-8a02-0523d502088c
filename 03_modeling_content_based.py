import pandas as pd
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import pickle
import os

# --- 0. T<PERSON><PERSON> thư mục models ---
MODEL_DIR = "models"
if not os.path.exists(MODEL_DIR):
    os.makedirs(MODEL_DIR)

# --- 1. Tải dữ liệu ---
DATA_FILE = "output/movies_for_content_based.csv"
movies_df = pd.read_csv(DATA_FILE)

# --- 2. Vector hóa với TF-IDF ---
tfidf = TfidfVectorizer(stop_words='english')
movies_df['soup'] = movies_df['soup'].fillna('')
tfidf_matrix = tfidf.fit_transform(movies_df['soup'])

# --- 3. Tính toán ma trận tương đồng Cosine ---
cosine_sim = cosine_similarity(tfidf_matrix, tfidf_matrix)

# --- 4. <PERSON><PERSON><PERSON> c<PERSON><PERSON> đối tượng ---
pickle.dump(cosine_sim, open(os.path.join(MODEL_DIR, 'content_cosine_sim.pkl'), 'wb'))
pickle.dump(movies_df, open(os.path.join(MODEL_DIR, 'content_movies_df.pkl'), 'wb'))
print("Đã lưu ma trận tương đồng và DataFrame phim cho mô hình Lọc Nội dung.")

# --- 5. Ví dụ hàm đề xuất ---
indices = pd.Series(movies_df.index, index=movies_df['title_cleaned']).drop_duplicates()
def get_content_based_recommendations(title, cosine_sim_matrix, df, top_n=10):
    if title not in indices:
        return f"Phim '{title}' không có trong bộ dữ liệu."
    idx = indices[title]
    sim_scores = list(enumerate(cosine_sim_matrix[idx]))
    sim_scores = sorted(sim_scores, key=lambda x: x, reverse=True)
    sim_scores = sim_scores[1:top_n+1]
    movie_indices = [i for i in sim_scores]
    return df['title_cleaned'].iloc[movie_indices]

# Ví dụ
recommendations = get_content_based_recommendations('Toy Story', cosine_sim, movies_df)
print("\nVí dụ đề xuất cho 'Toy Story':")
print(recommendations)